# 聚合说明书生成

## 任务描述
请基于以下相关的流程说明书，生成一个综合的聚合说明书。

## 聚合信息
- **聚合类型**: {aggregationType}
- **聚合描述**: {description}

## 相关说明书
{docsContent}

## 生成要求

### 内容整合原则
1. **完整性**: 整合所有相关流程的核心信息
2. **连贯性**: 确保内容逻辑清晰，结构完整
3. **去重性**: 避免重复内容，突出互补信息
4. **关联性**: 突出各个流程之间的关联性和整体业务价值

### 标题要求
- 简洁明了，体现聚合的核心功能
- 使用中文，避免技术术语
- 一句话概括整个聚合的业务价值

### 摘要要求
- 200字以内的简洁摘要
- 包含业务简述和关键词
- 避免技术术语，专注业务语义
- 用于生成向量检索，应包含相关业务关键词

### 内容要求
- 使用Markdown格式
- 结构清晰，层次分明
- 包含以下主要部分：
  - **包含的流程文档**: 明确列出本聚合说明书包含的所有流程文档
  - **业务概述**: 整体业务功能和价值描述
  - **核心流程说明**: 详细的业务流程描述
  - **各流程间的关联关系**: 流程之间的依赖和协作关系
  - **关键业务规则**: 重要的业务规则和约束
  - **异常处理机制**: 错误处理和异常情况说明

## 输出格式
请严格按照以下JSON格式返回结果，不要添加任何其他内容：

```json
\{
  "title": "聚合说明书标题",
  "summary": "聚合说明书摘要",
  "content": "# 聚合说明书标题\n\n## 包含的流程文档\n- 文档标题1：\{documentId1\}\n- 文档标题2：\{documentId2\}\n\n## 业务概述\n...\n\n## 核心流程说明\n...\n\n## 各流程间的关联关系\n...\n\n## 关键业务规则\n...\n\n## 异常处理机制\n...\n\n## 业务价值和意义\n..."
\}
```

## 特别注意
- 只返回JSON对象，不要包含其他解释文字
- content字段必须是完整的Markdown格式内容
- **必须在content开头明确列出包含的流程文档**，格式为：
  ```markdown
  ## 包含的流程文档
  - 订单创建说明书：\{文档id\}
  - 订单修改说明书：\{文档id\}
  - 订单取消说明书：\{文档id\}
  ```
- 确保生成的文档比单个流程说明书更有价值
- 突出整体业务流程的完整性和一致性
- documentId必须使用实际的文档ID数值，不要使用占位符
