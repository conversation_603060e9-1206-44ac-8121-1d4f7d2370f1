# 批量说明书查询API文档

## 概述

本文档描述了批量查询说明书相关的REST API接口，包括批量聚合说明书查询和批量流程说明书查询功能。

## 基础信息

- **Base URL**: `/api/documentation/query`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **请求方法**: POST

## 接口列表

### 1. 批量查询聚合说明书

根据说明书ID列表批量查询聚合说明书的内容。

#### 请求信息

- **URL**: `POST /api/documentation/query/aggregated/batch`
- **方法**: POST
- **请求体**: BatchQueryRequest

#### 请求参数

```json
{
  "documentIds": ["id1", "id2", "id3"]
}
```

| 字段名 | 类型 | 必填 | 描述 | 限制 |
|--------|------|------|------|------|
| documentIds | List<String> | 是 | 说明书ID列表 | 最多100个ID |

#### 请求示例

```http
POST /api/documentation/query/aggregated/batch
Content-Type: application/json

{
  "documentIds": [
    "1234567890123456789",
    "9876543210987654321",
    "1111222233334444555"
  ]
}
```

#### 响应格式

**成功响应 (200 OK)**:

```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": "1234567890123456789",
      "content": "# 聚合说明书1\n\n这里是第一个聚合说明书的详细内容..."
    },
    {
      "id": "9876543210987654321",
      "content": "# 聚合说明书2\n\n这里是第二个聚合说明书的详细内容..."
    }
  ]
}
```

**参数错误 (400 Bad Request)**:

```json
{
  "success": false,
  "message": "说明书ID列表不能为空",
  "data": null
}
```

### 2. 批量查询流程说明书

根据说明书ID列表批量查询流程说明书的内容及其关联的方法信息。

#### 请求信息

- **URL**: `POST /api/documentation/query/process/batch`
- **方法**: POST
- **请求体**: BatchQueryRequest

#### 请求参数

```json
{
  "documentIds": ["id1", "id2", "id3"]
}
```

#### 请求示例

```http
POST /api/documentation/query/process/batch
Content-Type: application/json

{
  "documentIds": [
    "1234567890123456789",
    "9876543210987654321"
  ]
}
```

#### 响应格式

**成功响应 (200 OK)**:

```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": "1234567890123456789",
      "content": "# 流程说明书1\n\n这里是第一个流程说明书的详细内容...",
      "entryPointId": "entry-point-123",
      "entryPointName": "订单处理入口点",
      "methods": [
        {
          "id": "method-id-1",
          "methodId": "method-node-123",
          "methodName": "com.example.OrderController.processOrder",
          "description": "处理订单的主要方法",
          "signature": "public ResponseEntity<Order> processOrder(OrderRequest request)",
          "className": "com.example.OrderController",
          "callLevel": 0,
          "methodType": "ENTRY_POINT"
        }
      ]
    },
    {
      "id": "9876543210987654321",
      "content": "# 流程说明书2\n\n这里是第二个流程说明书的详细内容...",
      "entryPointId": "entry-point-456",
      "entryPointName": "用户管理入口点",
      "methods": [
        {
          "id": "method-id-2",
          "methodId": "method-node-456",
          "methodName": "com.example.UserController.manageUser",
          "description": "管理用户的主要方法",
          "signature": "public ResponseEntity<User> manageUser(UserRequest request)",
          "className": "com.example.UserController",
          "callLevel": 0,
          "methodType": "ENTRY_POINT"
        }
      ]
    }
  ]
}
```

## 数据模型

### BatchQueryRequest

批量查询请求对象

| 字段名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| documentIds | List<String> | 是 | 说明书ID列表 | @NotEmpty, @Size(max=100) |

### AggregatedDocumentationResponse

聚合说明书响应对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | String | 说明书ID |
| content | String | 说明书内容 |

### ProcessDocumentationResponse

流程说明书响应对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | String | 说明书ID |
| content | String | 说明书内容 |
| entryPointId | String | 入口点ID |
| entryPointName | String | 入口点名称 |
| methods | List<MethodInfo> | 关联的方法信息列表 |

### MethodInfo

方法信息对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | String | 方法记录ID |
| methodId | String | 方法节点ID |
| methodName | String | 方法全限定名 |
| description | String | 方法描述 |
| signature | String | 方法签名 |
| className | String | 所属类名 |
| callLevel | Integer | 调用层级 |
| methodType | String | 方法类型 |

## 特性说明

### 1. 批量处理
- 支持一次查询多个说明书
- 单次请求最多支持100个ID
- 部分失败不影响其他记录的查询

### 2. 错误处理
- 不存在的ID会被跳过，不返回错误
- 单个记录查询失败不影响其他记录
- 返回成功查询到的记录列表

### 3. 性能优化
- 每个ID独立查询，避免单点失败
- 详细的日志记录便于问题排查
- 支持部分成功的场景

## 错误码说明

| HTTP状态码 | 说明 |
|------------|------|
| 200 | 请求成功（包括部分成功） |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 使用示例

### 使用curl批量查询聚合说明书

```bash
curl -X POST "http://localhost:8080/documentation/api/documentation/query/aggregated/batch" \
     -H "Content-Type: application/json" \
     -d '{
       "documentIds": [
         "1234567890123456789",
         "9876543210987654321"
       ]
     }'
```

### 使用curl批量查询流程说明书

```bash
curl -X POST "http://localhost:8080/documentation/api/documentation/query/process/batch" \
     -H "Content-Type: application/json" \
     -d '{
       "documentIds": [
         "1234567890123456789",
         "9876543210987654321"
       ]
     }'
```

### 使用JavaScript批量查询

```javascript
// 批量查询聚合说明书
async function batchGetAggregatedDocumentation(documentIds) {
    try {
        const response = await fetch('/api/documentation/query/aggregated/batch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                documentIds: documentIds
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log(`成功查询到 ${result.data.length} 个聚合说明书`);
            result.data.forEach(doc => {
                console.log(`ID: ${doc.id}, 内容长度: ${doc.content.length}`);
            });
        } else {
            console.error('查询失败:', result.message);
        }
    } catch (error) {
        console.error('请求错误:', error);
    }
}

// 批量查询流程说明书
async function batchGetProcessDocumentation(documentIds) {
    try {
        const response = await fetch('/api/documentation/query/process/batch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                documentIds: documentIds
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log(`成功查询到 ${result.data.length} 个流程说明书`);
            result.data.forEach(doc => {
                console.log(`ID: ${doc.id}, 方法数量: ${doc.methods.length}`);
            });
        } else {
            console.error('查询失败:', result.message);
        }
    } catch (error) {
        console.error('请求错误:', error);
    }
}

// 使用示例
const documentIds = ['1234567890123456789', '9876543210987654321'];
batchGetAggregatedDocumentation(documentIds);
batchGetProcessDocumentation(documentIds);
```

## 注意事项

1. **ID格式**: 说明书ID为32位字符串格式的分布式ID
2. **批量限制**: 单次请求最多支持100个ID
3. **部分成功**: 即使部分ID查询失败，也会返回成功查询到的记录
4. **内容格式**: 说明书内容通常为Markdown格式
5. **性能考虑**: 批量查询会并发处理，但仍需注意请求频率
6. **空值处理**: 请求中不能包含null或空字符串的ID
7. **日志记录**: 服务端会记录详细的查询日志，便于问题排查
