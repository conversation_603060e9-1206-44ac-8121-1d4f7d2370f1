package com.puti.code.documentation.service;

import com.puti.code.base.entity.rdb.AggregatedDocumentation;
import com.puti.code.base.entity.rdb.Documentation;
import com.puti.code.base.entity.rdb.DocumentationMethod;
import com.puti.code.documentation.controller.response.AggregatedDocumentationResponse;
import com.puti.code.documentation.controller.response.ProcessDocumentationResponse;
import com.puti.code.documentation.repository.sql.AggregatedDocumentationRepository;
import com.puti.code.documentation.repository.sql.DocumentationMethodRepository;
import com.puti.code.documentation.repository.sql.DocumentationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 说明书查询服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DocumentationQueryService {

    @Autowired
    private AggregatedDocumentationRepository aggregatedDocumentationRepository;

    @Autowired
    private DocumentationRepository documentationRepository;

    @Autowired
    private DocumentationMethodRepository documentationMethodRepository;

    /**
     * 根据ID查询聚合说明书
     *
     * @param documentId 说明书ID
     * @return 聚合说明书响应DTO
     */
    public Optional<AggregatedDocumentationResponse> getAggregatedDocumentation(String documentId) {
        try {
            log.info("查询聚合说明书，ID: {}", documentId);

            Optional<AggregatedDocumentation> aggregatedDocOpt = aggregatedDocumentationRepository.findById(documentId);
            if (aggregatedDocOpt.isEmpty()) {
                log.warn("未找到聚合说明书，ID: {}", documentId);
                return Optional.empty();
            }

            AggregatedDocumentation aggregatedDoc = aggregatedDocOpt.get();
            AggregatedDocumentationResponse response = AggregatedDocumentationResponse.builder()
                    .id(aggregatedDoc.getId())
                    .content(aggregatedDoc.getContent())
                    .build();

            log.info("成功查询到聚合说明书，ID: {}", documentId);
            return Optional.of(response);

        } catch (Exception e) {
            log.error("查询聚合说明书时发生错误，ID: {}", documentId, e);
            return Optional.empty();
        }
    }

    /**
     * 根据ID查询流程说明书及其关联方法
     *
     * @param documentId 说明书ID
     * @return 流程说明书响应DTO
     */
    public Optional<ProcessDocumentationResponse> getProcessDocumentation(String documentId) {
        try {
            log.info("查询流程说明书，ID: {}", documentId);

            // 查询说明书基本信息
            Optional<Documentation> documentationOpt = documentationRepository.findById(documentId);
            if (documentationOpt.isEmpty()) {
                log.warn("未找到流程说明书，ID: {}", documentId);
                return Optional.empty();
            }

            Documentation documentation = documentationOpt.get();

            // 查询关联的方法信息
            List<DocumentationMethod> methods = documentationMethodRepository.findByDocumentationId(documentId);

            // 转换方法信息
//            List<ProcessDocumentationResponse.MethodInfo> methodInfos = methods.stream()
//                    .map(this::convertToMethodInfo)
//                    .collect(Collectors.toList());

            ProcessDocumentationResponse response = ProcessDocumentationResponse.builder()
                    .id(documentation.getId())
                    .content(documentation.getContent())
                    .entryPointId(documentation.getEntryPointId())
                    .entryPointName(documentation.getEntryPointName())
//                    .methods(methodInfos)
                    .build();

            log.info("成功查询到流程说明书，ID: {}", documentId);
            return Optional.of(response);

        } catch (Exception e) {
            log.error("查询流程说明书时发生错误，ID: {}", documentId, e);
            return Optional.empty();
        }
    }

//    /**
//     * 将DocumentationMethod转换为MethodInfo
//     *
//     * @param method 方法实体
//     * @return 方法信息DTO
//     */
//    private ProcessDocumentationResponse.MethodInfo convertToMethodInfo(DocumentationMethod method) {
//        return ProcessDocumentationResponse.MethodInfo.builder()
//                .id(method.getId())
//                .methodId(method.getMethodId())
//                .methodName(method.getMethodName())
//                .description(method.getDescription())
//                .signature(method.getSignature())
//                .className(method.getClassName())
//                .callLevel(method.getCallLevel())
//                .methodType(method.getMethodType() != null ? method.getMethodType().name() : null)
//                .build();
//    }
}
