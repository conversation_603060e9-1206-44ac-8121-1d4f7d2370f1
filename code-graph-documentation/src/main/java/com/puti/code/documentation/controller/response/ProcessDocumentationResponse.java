package com.puti.code.documentation.controller.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 流程说明书查询响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessDocumentationResponse {
    
    /**
     * 说明书ID
     */
    private String id;
    
    /**
     * 说明书内容
     */
    private String content;
    
    /**
     * 入口点ID
     */
    private String entryPointId;
    
    /**
     * 入口点名称
     */
    private String entryPointName;
    
    /**
     * 关联的方法信息列表
     */
    private List<MethodInfo> methods;
    
    /**
     * 方法信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MethodInfo {
        
        /**
         * 方法ID
         */
        private String id;
        
        /**
         * 方法节点ID
         */
        private String methodId;
        
        /**
         * 方法全限定名
         */
        private String methodName;
        
        /**
         * 方法描述
         */
        private String description;
        
        /**
         * 方法签名
         */
        private String signature;
        
        /**
         * 所属类名
         */
        private String className;
        
        /**
         * 调用层级
         */
        private Integer callLevel;
        
        /**
         * 方法类型
         */
        private String methodType;
    }
}
