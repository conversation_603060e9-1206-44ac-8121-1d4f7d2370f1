package com.puti.code.documentation.controller;

import com.puti.code.documentation.controller.response.AggregatedDocumentationResponse;
import com.puti.code.documentation.controller.response.ProcessDocumentationResponse;
import com.puti.code.documentation.service.DocumentationQueryService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * 说明书查询控制器
 * 提供聚合说明书和流程说明书的查询接口
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/documentation/query")
public class DocumentationQueryController {
    
    @Autowired
    private DocumentationQueryService documentationQueryService;
    
    /**
     * 查询聚合说明书
     * 
     * @param documentId 说明书ID
     * @return 聚合说明书内容
     */
    @GetMapping("/aggregated/{documentId}")
    public ResponseEntity<?> getAggregatedDocumentation(@PathVariable String documentId) {
        try {
            log.info("接收到聚合说明书查询请求，documentId: {}", documentId);
            
            if (documentId == null || documentId.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "说明书ID不能为空", null));
            }
            
            Optional<AggregatedDocumentationResponse> result = 
                    documentationQueryService.getAggregatedDocumentation(documentId);
            
            if (result.isPresent()) {
                return ResponseEntity.ok()
                        .body(new ApiResponse<>(true, "查询成功", result.get()));
            } else {
                return ResponseEntity.notFound()
                        .build();
            }
            
        } catch (Exception e) {
            log.error("查询聚合说明书时发生错误，documentId: {}", documentId, e);
            return ResponseEntity.internalServerError()
                    .body(new ApiResponse<>(false, "查询失败: " + e.getMessage(), null));
        }
    }
    
    /**
     * 查询流程说明书
     * 
     * @param documentId 说明书ID
     * @return 流程说明书内容及关联方法信息
     */
    @GetMapping("/process/{documentId}")
    public ResponseEntity<?> getProcessDocumentation(@PathVariable String documentId) {
        try {
            log.info("接收到流程说明书查询请求，documentId: {}", documentId);
            
            if (documentId == null || documentId.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(false, "说明书ID不能为空", null));
            }
            
            Optional<ProcessDocumentationResponse> result = 
                    documentationQueryService.getProcessDocumentation(documentId);
            
            if (result.isPresent()) {
                return ResponseEntity.ok()
                        .body(new ApiResponse<>(true, "查询成功", result.get()));
            } else {
                return ResponseEntity.notFound()
                        .build();
            }
            
        } catch (Exception e) {
            log.error("查询流程说明书时发生错误，documentId: {}", documentId, e);
            return ResponseEntity.internalServerError()
                    .body(new ApiResponse<>(false, "查询失败: " + e.getMessage(), null));
        }
    }
    
    /**
     * API响应包装类
     */
    @Setter
    @Getter
    public static class ApiResponse<T> {
        // Setters
        // Getters
        private boolean success;
        private String message;
        private T data;
        
        public ApiResponse(boolean success, String message, T data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }

    }
}
