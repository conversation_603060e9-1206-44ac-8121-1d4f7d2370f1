package com.puti.code.documentation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.puti.code.base.entity.rdb.AggregatedDocumentation;
import com.puti.code.base.entity.rdb.Documentation;
import com.puti.code.base.entity.rdb.DocumentationMethod;
import com.puti.code.documentation.controller.request.BatchQueryRequest;
import com.puti.code.documentation.repository.sql.AggregatedDocumentationRepository;
import com.puti.code.documentation.repository.sql.DocumentationRepository;
import com.puti.code.documentation.repository.sql.DocumentationMethodRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 批量查询说明书接口测试
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class DocumentationBatchQueryTest {
    
    @Autowired
    private WebApplicationContext webApplicationContext;
    
    @Autowired
    private AggregatedDocumentationRepository aggregatedDocumentationRepository;
    
    @Autowired
    private DocumentationRepository documentationRepository;
    
    @Autowired
    private DocumentationMethodRepository documentationMethodRepository;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private MockMvc mockMvc;
    
    private static final String TEST_AGGREGATED_DOC_ID_1 = "test-agg-doc-1-123456789012";
    private static final String TEST_AGGREGATED_DOC_ID_2 = "test-agg-doc-2-123456789012";
    private static final String TEST_PROCESS_DOC_ID_1 = "test-proc-doc-1-123456789012";
    private static final String TEST_PROCESS_DOC_ID_2 = "test-proc-doc-2-123456789012";
    private static final String TEST_METHOD_ID_1 = "test-method-1-123456789012";
    private static final String TEST_METHOD_ID_2 = "test-method-2-123456789012";
    
    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        setupTestData();
    }
    
    /**
     * 设置测试数据
     */
    private void setupTestData() {
        try {
            // 创建测试用聚合说明书1
            AggregatedDocumentation aggregatedDoc1 = AggregatedDocumentation.builder()
                    .id(TEST_AGGREGATED_DOC_ID_1)
                    .title("测试聚合说明书1")
                    .summary("这是第一个测试聚合说明书")
                    .content("# 测试聚合说明书1\n\n这是第一个聚合说明书的内容")
                    .projectId("test-project")
                    .branchName("main")
                    .aggregationType("测试聚合")
                    .status(AggregatedDocumentation.AggregationStatus.COMPLETED)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            
            // 创建测试用聚合说明书2
            AggregatedDocumentation aggregatedDoc2 = AggregatedDocumentation.builder()
                    .id(TEST_AGGREGATED_DOC_ID_2)
                    .title("测试聚合说明书2")
                    .summary("这是第二个测试聚合说明书")
                    .content("# 测试聚合说明书2\n\n这是第二个聚合说明书的内容")
                    .projectId("test-project")
                    .branchName("main")
                    .aggregationType("测试聚合")
                    .status(AggregatedDocumentation.AggregationStatus.COMPLETED)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            
            // 插入聚合说明书
            if (aggregatedDocumentationRepository.findById(TEST_AGGREGATED_DOC_ID_1).isEmpty()) {
                aggregatedDocumentationRepository.save(aggregatedDoc1);
                log.info("创建测试聚合说明书1: {}", TEST_AGGREGATED_DOC_ID_1);
            }
            if (aggregatedDocumentationRepository.findById(TEST_AGGREGATED_DOC_ID_2).isEmpty()) {
                aggregatedDocumentationRepository.save(aggregatedDoc2);
                log.info("创建测试聚合说明书2: {}", TEST_AGGREGATED_DOC_ID_2);
            }
            
            // 创建测试用流程说明书1
            Documentation processDoc1 = Documentation.builder()
                    .id(TEST_PROCESS_DOC_ID_1)
                    .entryPointId("test-entry-point-1")
                    .entryPointName("测试订单处理入口点1")
                    .title("测试流程说明书1")
                    .summary("这是第一个测试流程说明书")
                    .content("# 测试流程说明书1\n\n这是第一个流程说明书的内容")
                    .level(1)
                    .status(Documentation.DocumentationStatus.COMPLETED)
                    .version(1)
                    .isFinalVersion(true)
                    .projectId("test-project")
                    .branchName("main")
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            
            // 创建测试用流程说明书2
            Documentation processDoc2 = Documentation.builder()
                    .id(TEST_PROCESS_DOC_ID_2)
                    .entryPointId("test-entry-point-2")
                    .entryPointName("测试用户管理入口点2")
                    .title("测试流程说明书2")
                    .summary("这是第二个测试流程说明书")
                    .content("# 测试流程说明书2\n\n这是第二个流程说明书的内容")
                    .level(1)
                    .status(Documentation.DocumentationStatus.COMPLETED)
                    .version(1)
                    .isFinalVersion(true)
                    .projectId("test-project")
                    .branchName("main")
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            
            // 插入流程说明书
            if (documentationRepository.findById(TEST_PROCESS_DOC_ID_1).isEmpty()) {
                documentationRepository.save(processDoc1);
                log.info("创建测试流程说明书1: {}", TEST_PROCESS_DOC_ID_1);
            }
            if (documentationRepository.findById(TEST_PROCESS_DOC_ID_2).isEmpty()) {
                documentationRepository.save(processDoc2);
                log.info("创建测试流程说明书2: {}", TEST_PROCESS_DOC_ID_2);
            }
            
            // 创建测试用方法信息1
            DocumentationMethod method1 = DocumentationMethod.builder()
                    .id(TEST_METHOD_ID_1)
                    .documentationId(TEST_PROCESS_DOC_ID_1)
                    .methodId("test-method-node-1")
                    .methodName("com.example.OrderController.processOrder")
                    .methodType(DocumentationMethod.MethodType.ENTRY_POINT)
                    .callLevel(0)
                    .description("处理订单的主要入口方法")
                    .signature("public ResponseEntity<Order> processOrder(OrderRequest request)")
                    .className("com.example.OrderController")
                    .createdAt(LocalDateTime.now())
                    .build();
            
            // 创建测试用方法信息2
            DocumentationMethod method2 = DocumentationMethod.builder()
                    .id(TEST_METHOD_ID_2)
                    .documentationId(TEST_PROCESS_DOC_ID_2)
                    .methodId("test-method-node-2")
                    .methodName("com.example.UserController.manageUser")
                    .methodType(DocumentationMethod.MethodType.ENTRY_POINT)
                    .callLevel(0)
                    .description("管理用户的主要入口方法")
                    .signature("public ResponseEntity<User> manageUser(UserRequest request)")
                    .className("com.example.UserController")
                    .createdAt(LocalDateTime.now())
                    .build();
            
            // 插入方法信息
            if (documentationMethodRepository.findById(TEST_METHOD_ID_1).isEmpty()) {
                documentationMethodRepository.save(method1);
                log.info("创建测试方法信息1: {}", TEST_METHOD_ID_1);
            }
            if (documentationMethodRepository.findById(TEST_METHOD_ID_2).isEmpty()) {
                documentationMethodRepository.save(method2);
                log.info("创建测试方法信息2: {}", TEST_METHOD_ID_2);
            }
            
        } catch (Exception e) {
            log.warn("设置测试数据时发生错误（可能数据已存在）: {}", e.getMessage());
        }
    }
    
    @Test
    public void testBatchGetAggregatedDocumentation_Success() throws Exception {
        List<String> documentIds = Arrays.asList(TEST_AGGREGATED_DOC_ID_1, TEST_AGGREGATED_DOC_ID_2);
        BatchQueryRequest request = BatchQueryRequest.builder()
                .documentIds(documentIds)
                .build();
        
        mockMvc.perform(post("/api/documentation/query/aggregated/batch")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").exists())
                .andExpect(jsonPath("$.data[0].content").exists())
                .andExpect(jsonPath("$.data[1].id").exists())
                .andExpect(jsonPath("$.data[1].content").exists());
    }
    
    @Test
    public void testBatchGetProcessDocumentation_Success() throws Exception {
        List<String> documentIds = Arrays.asList(TEST_PROCESS_DOC_ID_1, TEST_PROCESS_DOC_ID_2);
        BatchQueryRequest request = BatchQueryRequest.builder()
                .documentIds(documentIds)
                .build();
        
        mockMvc.perform(post("/api/documentation/query/process/batch")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").exists())
                .andExpect(jsonPath("$.data[0].content").exists())
                .andExpect(jsonPath("$.data[0].entryPointId").exists())
                .andExpect(jsonPath("$.data[0].entryPointName").exists())
                .andExpect(jsonPath("$.data[0].methods").isArray());
    }
    
    @Test
    public void testBatchQuery_EmptyRequest() throws Exception {
        BatchQueryRequest request = BatchQueryRequest.builder()
                .documentIds(Arrays.asList())
                .build();
        
        mockMvc.perform(post("/api/documentation/query/aggregated/batch")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("说明书ID列表不能为空"));
    }
    
    @Test
    public void testBatchQuery_PartialResults() throws Exception {
        List<String> documentIds = Arrays.asList(TEST_AGGREGATED_DOC_ID_1, "non-existent-id");
        BatchQueryRequest request = BatchQueryRequest.builder()
                .documentIds(documentIds)
                .build();
        
        mockMvc.perform(post("/api/documentation/query/aggregated/batch")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1)); // 只返回找到的记录
    }
}
