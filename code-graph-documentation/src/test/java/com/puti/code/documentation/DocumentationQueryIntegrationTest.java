package com.puti.code.documentation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.puti.code.base.entity.rdb.AggregatedDocumentation;
import com.puti.code.base.entity.rdb.Documentation;
import com.puti.code.base.entity.rdb.DocumentationMethod;
import com.puti.code.documentation.repository.sql.AggregatedDocumentationRepository;
import com.puti.code.documentation.repository.sql.DocumentationRepository;
import com.puti.code.documentation.repository.sql.DocumentationMethodRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 说明书查询接口集成测试
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class DocumentationQueryIntegrationTest {
    
    @Autowired
    private WebApplicationContext webApplicationContext;
    
    @Autowired
    private AggregatedDocumentationRepository aggregatedDocumentationRepository;

    @Autowired
    private DocumentationRepository documentationRepository;

    @Autowired
    private DocumentationMethodRepository documentationMethodRepository;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private MockMvc mockMvc;
    
    private static final String TEST_AGGREGATED_DOC_ID = "test-aggregated-doc-123456789012";
    private static final String TEST_PROCESS_DOC_ID = "test-process-doc-123456789012";
    private static final String TEST_METHOD_ID = "test-method-123456789012";
    
    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        setupTestData();
    }
    
    /**
     * 设置测试数据
     */
    private void setupTestData() {
        try {
            // 创建测试用聚合说明书
            AggregatedDocumentation aggregatedDoc = AggregatedDocumentation.builder()
                    .id(TEST_AGGREGATED_DOC_ID)
                    .title("测试聚合说明书")
                    .summary("这是一个用于测试的聚合说明书")
                    .content("# 测试聚合说明书\n\n这是聚合说明书的测试内容，包含了多个流程的汇总信息。\n\n## 主要功能\n\n1. 订单处理流程\n2. 用户管理流程\n3. 支付处理流程")
                    .projectId("test-project")
                    .branchName("main")
                    .aggregationType("测试聚合")
                    .status(AggregatedDocumentation.AggregationStatus.COMPLETED)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            
            // 插入聚合说明书（如果不存在）
            if (aggregatedDocumentationRepository.findById(TEST_AGGREGATED_DOC_ID).isEmpty()) {
                aggregatedDocumentationRepository.save(aggregatedDoc);
                log.info("创建测试聚合说明书: {}", TEST_AGGREGATED_DOC_ID);
            }
            
            // 创建测试用流程说明书
            Documentation processDoc = Documentation.builder()
                    .id(TEST_PROCESS_DOC_ID)
                    .entryPointId("test-entry-point-123")
                    .entryPointName("测试订单处理入口点")
                    .title("测试流程说明书")
                    .summary("这是一个用于测试的流程说明书")
                    .content("# 测试流程说明书\n\n这是流程说明书的测试内容。\n\n## 流程概述\n\n本流程主要处理订单相关的业务逻辑。\n\n## 主要步骤\n\n1. 接收订单请求\n2. 验证订单信息\n3. 处理订单\n4. 返回处理结果")
                    .level(1)
                    .status(Documentation.DocumentationStatus.COMPLETED)
                    .version(1)
                    .isFinalVersion(true)
                    .projectId("test-project")
                    .branchName("main")
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            
            // 插入流程说明书（如果不存在）
            if (documentationRepository.findById(TEST_PROCESS_DOC_ID).isEmpty()) {
                documentationRepository.save(processDoc);
                log.info("创建测试流程说明书: {}", TEST_PROCESS_DOC_ID);
            }
            
            // 创建测试用方法信息
            DocumentationMethod method = DocumentationMethod.builder()
                    .id(TEST_METHOD_ID)
                    .documentationId(TEST_PROCESS_DOC_ID)
                    .methodId("test-method-node-123")
                    .methodName("com.example.OrderController.processOrder")
                    .methodType(DocumentationMethod.MethodType.ENTRY_POINT)
                    .callLevel(0)
                    .description("处理订单的主要入口方法")
                    .signature("public ResponseEntity<Order> processOrder(OrderRequest request)")
                    .className("com.example.OrderController")
                    .createdAt(LocalDateTime.now())
                    .build();
            
            // 插入方法信息（如果不存在）
            if (documentationMethodRepository.findById(TEST_METHOD_ID).isEmpty()) {
                documentationMethodRepository.save(method);
                log.info("创建测试方法信息: {}", TEST_METHOD_ID);
            }
            
        } catch (Exception e) {
            log.warn("设置测试数据时发生错误（可能数据已存在）: {}", e.getMessage());
        }
    }
    
    @Test
    public void testGetAggregatedDocumentation_Success() throws Exception {
        mockMvc.perform(get("/api/documentation/query/aggregated/{documentId}", TEST_AGGREGATED_DOC_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.id").value(TEST_AGGREGATED_DOC_ID))
                .andExpect(jsonPath("$.data.content").isNotEmpty());
    }
    
    @Test
    public void testGetAggregatedDocumentation_NotFound() throws Exception {
        String nonExistentId = "non-existent-id-123456789012";
        
        mockMvc.perform(get("/api/documentation/query/aggregated/{documentId}", nonExistentId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound());
    }
    
    @Test
    public void testGetAggregatedDocumentation_EmptyId() throws Exception {
        mockMvc.perform(get("/api/documentation/query/aggregated/{documentId}", "")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("说明书ID不能为空"));
    }
    
    @Test
    public void testGetProcessDocumentation_Success() throws Exception {
        mockMvc.perform(get("/api/documentation/query/process/{documentId}", TEST_PROCESS_DOC_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.id").value(TEST_PROCESS_DOC_ID))
                .andExpect(jsonPath("$.data.content").isNotEmpty())
                .andExpect(jsonPath("$.data.entryPointId").value("test-entry-point-123"))
                .andExpect(jsonPath("$.data.entryPointName").value("测试订单处理入口点"))
                .andExpect(jsonPath("$.data.methods").isArray())
                .andExpect(jsonPath("$.data.methods[0].id").value(TEST_METHOD_ID))
                .andExpect(jsonPath("$.data.methods[0].methodName").value("com.example.OrderController.processOrder"));
    }
    
    @Test
    public void testGetProcessDocumentation_NotFound() throws Exception {
        String nonExistentId = "non-existent-id-123456789012";
        
        mockMvc.perform(get("/api/documentation/query/process/{documentId}", nonExistentId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound());
    }
    
    @Test
    public void testGetProcessDocumentation_EmptyId() throws Exception {
        mockMvc.perform(get("/api/documentation/query/process/{documentId}", "")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("说明书ID不能为空"));
    }
}
