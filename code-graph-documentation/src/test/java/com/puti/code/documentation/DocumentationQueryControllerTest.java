package com.puti.code.documentation;

import com.puti.code.base.entity.rdb.AggregatedDocumentation;
import com.puti.code.base.entity.rdb.Documentation;
import com.puti.code.base.entity.rdb.DocumentationMethod;
import com.puti.code.documentation.controller.response.AggregatedDocumentationResponse;
import com.puti.code.documentation.controller.response.ProcessDocumentationResponse;
import com.puti.code.documentation.service.DocumentationQueryService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 说明书查询接口测试
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DocumentationQueryControllerTest {
    
    @Autowired
    private DocumentationQueryService documentationQueryService;
    
    @Test
    public void testGetAggregatedDocumentation() {
        // 测试查询聚合说明书
        String documentId = "test-aggregated-doc-id";
        
        Optional<AggregatedDocumentationResponse> result = 
                documentationQueryService.getAggregatedDocumentation(documentId);
        
        if (result.isPresent()) {
            AggregatedDocumentationResponse response = result.get();
            log.info("查询到聚合说明书:");
            log.info("ID: {}", response.getId());
            log.info("内容长度: {}", response.getContent() != null ? response.getContent().length() : 0);
        } else {
            log.info("未找到聚合说明书，ID: {}", documentId);
        }
    }
    
    @Test
    public void testGetProcessDocumentation() {
        // 测试查询流程说明书
        String documentId = "test-process-doc-id";
        
        Optional<ProcessDocumentationResponse> result = 
                documentationQueryService.getProcessDocumentation(documentId);
        
        if (result.isPresent()) {
            ProcessDocumentationResponse response = result.get();
            log.info("查询到流程说明书:");
            log.info("ID: {}", response.getId());
            log.info("入口点ID: {}", response.getEntryPointId());
            log.info("入口点名称: {}", response.getEntryPointName());
            log.info("内容长度: {}", response.getContent() != null ? response.getContent().length() : 0);
            log.info("关联方法数量: {}", response.getMethods() != null ? response.getMethods().size() : 0);
            
            if (response.getMethods() != null && !response.getMethods().isEmpty()) {
                log.info("方法信息:");
                response.getMethods().forEach(method -> {
                    log.info("  - 方法名: {}", method.getMethodName());
                    log.info("    类名: {}", method.getClassName());
                    log.info("    调用层级: {}", method.getCallLevel());
                    log.info("    方法类型: {}", method.getMethodType());
                });
            }
        } else {
            log.info("未找到流程说明书，ID: {}", documentId);
        }
    }
    
    @Test
    public void testCreateTestData() {
        // 这个测试用于创建测试数据（如果需要的话）
        log.info("如果需要创建测试数据，可以在这里实现");
        
        // 示例：创建测试用的聚合说明书数据
        AggregatedDocumentation testAggregatedDoc = AggregatedDocumentation.builder()
                .title("测试聚合说明书")
                .summary("这是一个测试用的聚合说明书摘要")
                .content("# 测试聚合说明书\n\n这是测试内容...")
                .projectId("test-project")
                .branchName("main")
                .aggregationType("测试类型")
                .status(AggregatedDocumentation.AggregationStatus.COMPLETED)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        
        // 示例：创建测试用的流程说明书数据
        Documentation testDoc = Documentation.builder()
                .entryPointId("test-entry-point")
                .entryPointName("测试入口点")
                .title("测试流程说明书")
                .summary("这是一个测试用的流程说明书摘要")
                .content("# 测试流程说明书\n\n这是测试内容...")
                .level(1)
                .status(Documentation.DocumentationStatus.COMPLETED)
                .version(1)
                .isFinalVersion(true)
                .projectId("test-project")
                .branchName("main")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        
        // 示例：创建测试用的方法信息数据
        DocumentationMethod testMethod = DocumentationMethod.builder()
                .methodId("test-method-node-id")
                .methodName("com.example.TestClass.testMethod")
                .methodType(DocumentationMethod.MethodType.ENTRY_POINT)
                .callLevel(0)
                .description("这是一个测试方法")
                .signature("public void testMethod(String param)")
                .className("com.example.TestClass")
                .createdAt(LocalDateTime.now())
                .build();
        
        log.info("测试数据创建完成");
    }
}
